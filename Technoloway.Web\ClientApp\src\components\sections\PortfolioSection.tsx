import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { ExternalLink, Briefcase, Calendar } from 'lucide-react';
import AnimatedSection from '../ui/AnimatedSection';
import Button from '../ui/Button';
import LoadingSpinner from '../ui/LoadingSpinner';
import { Project } from '../../types';
import { apiService } from '../../services/api';

const PortfolioSection: React.FC = () => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        const data = await apiService.getProjects(true); // Get featured projects
        setProjects(data.slice(0, 6)); // Show first 6 projects
      } catch (err) {
        setError('Failed to load projects');
        console.error('Error fetching projects:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, []);

  const handleProjectClick = (projectId: number) => {
    // Navigate to project details
    window.location.href = `/projects/${projectId}`;
  };

  const handleViewAllProjects = () => {
    // Navigate to projects page
    window.location.href = '/projects';
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'Ongoing';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short'
    });
  };

  if (loading) {
    return (
      <section className="py-24 bg-white">
        <div className="container mx-auto px-6">
          <div className="flex justify-center">
            <LoadingSpinner size="lg" />
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="py-24 bg-white">
        <div className="container mx-auto px-6">
          <div className="text-center text-red-600">{error}</div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-24 bg-white">
      <div className="container mx-auto px-6">
        {/* Section Header */}
        <AnimatedSection className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-secondary-900 mb-6">
            <span className="bg-gradient-to-r from-primary-600 to-accent-500 bg-clip-text text-transparent">
              Featured
            </span>{' '}
            Projects
          </h2>
          <p className="text-xl text-secondary-600 max-w-3xl mx-auto leading-relaxed">
            Discover our portfolio of successful projects that showcase our expertise in delivering innovative software solutions.
          </p>
        </AnimatedSection>

        {/* Projects Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {projects.map((project, index) => (
            <AnimatedSection key={project.id} delay={index + 1}>
              <motion.div
                className="bg-white rounded-2xl overflow-hidden shadow-modern hover:shadow-modern-lg transition-all duration-300 cursor-pointer group"
                whileHover={{ y: -8 }}
                onClick={() => handleProjectClick(project.id)}
              >
                {/* Project Image */}
                <div className="relative h-48 overflow-hidden">
                  <img
                    src={project.imageUrl || '/images/project-placeholder.jpg'}
                    alt={project.name}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  
                  {/* Overlay Content */}
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="text-center text-white">
                      <h4 className="text-lg font-bold mb-2">{project.name}</h4>
                      <p className="text-sm mb-4 px-4">{project.description}</p>
                      <div className="flex items-center justify-center text-primary-400">
                        <span className="mr-2">View Details</span>
                        <ExternalLink className="w-4 h-4" />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Project Content */}
                <div className="p-6">
                  <h3 className="text-xl font-bold text-secondary-900 mb-2 group-hover:text-primary-600 transition-colors duration-300">
                    {project.name}
                  </h3>
                  
                  <p className="text-secondary-600 mb-4 line-clamp-2">
                    {project.description}
                  </p>

                  {/* Project Meta */}
                  <div className="flex items-center justify-between text-sm text-secondary-500 mb-4">
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 mr-1" />
                      <span>{formatDate(project.projCompletionDate)}</span>
                    </div>
                    <span className="px-2 py-1 bg-accent-100 text-accent-700 rounded-full text-xs font-medium">
                      Completed
                    </span>
                  </div>

                  {/* Technologies */}
                  {project.technologies && project.technologies.length > 0 && (
                    <div className="flex flex-wrap gap-2 mb-4">
                      {project.technologies.slice(0, 3).map((tech, techIndex) => (
                        <span
                          key={techIndex}
                          className="px-2 py-1 bg-primary-100 text-primary-700 rounded-full text-xs font-medium"
                        >
                          {tech}
                        </span>
                      ))}
                      {project.technologies.length > 3 && (
                        <span className="px-2 py-1 bg-secondary-100 text-secondary-700 rounded-full text-xs font-medium">
                          +{project.technologies.length - 3} more
                        </span>
                      )}
                    </div>
                  )}

                  {/* Client */}
                  {project.clientName && (
                    <p className="text-sm text-secondary-500">
                      Client: <span className="font-medium">{project.clientName}</span>
                    </p>
                  )}
                </div>
              </motion.div>
            </AnimatedSection>
          ))}
        </div>

        {/* View All Projects Button */}
        <AnimatedSection delay={4} className="text-center">
          <Button
            variant="primary"
            size="lg"
            onClick={handleViewAllProjects}
            icon={<Briefcase className="w-5 h-5" />}
          >
            View All Projects
          </Button>
        </AnimatedSection>
      </div>
    </section>
  );
};

export default PortfolioSection;
