{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Vegan = createLucideIcon(\"Vegan\", [[\"path\", {\n  d: \"M2 2a26.6 26.6 0 0 1 10 20c.9-6.82 1.5-9.5 4-14\",\n  key: \"qiv7li\"\n}], [\"path\", {\n  d: \"M16 8c4 0 6-2 6-6-4 0-6 2-6 6\",\n  key: \"n7eohy\"\n}], [\"path\", {\n  d: \"M17.41 3.6a10 10 0 1 0 3 3\",\n  key: \"1dion0\"\n}]]);\nexport { <PERSON>n as default };", "map": {"version": 3, "names": ["Vegan", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\ClientApp\\node_modules\\lucide-react\\src\\icons\\vegan.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Vegan\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMiAyYTI2LjYgMjYuNiAwIDAgMSAxMCAyMGMuOS02LjgyIDEuNS05LjUgNC0xNCIgLz4KICA8cGF0aCBkPSJNMTYgOGM0IDAgNi0yIDYtNi00IDAtNiAyLTYgNiIgLz4KICA8cGF0aCBkPSJNMTcuNDEgMy42YTEwIDEwIDAgMSAwIDMgMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/vegan\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Vegan = createLucideIcon('Vegan', [\n  ['path', { d: 'M2 2a26.6 26.6 0 0 1 10 20c.9-6.82 1.5-9.5 4-14', key: 'qiv7li' }],\n  ['path', { d: 'M16 8c4 0 6-2 6-6-4 0-6 2-6 6', key: 'n7eohy' }],\n  ['path', { d: 'M17.41 3.6a10 10 0 1 0 3 3', key: '1dion0' }],\n]);\n\nexport default Vegan;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,KAAA,GAAQC,gBAAA,CAAiB,OAAS,GACtC,CAAC,MAAQ;EAAEC,CAAA,EAAG,iDAAmD;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChF,CAAC,MAAQ;EAAED,CAAA,EAAG,+BAAiC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9D,CAAC,MAAQ;EAAED,CAAA,EAAG,4BAA8B;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC5D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}