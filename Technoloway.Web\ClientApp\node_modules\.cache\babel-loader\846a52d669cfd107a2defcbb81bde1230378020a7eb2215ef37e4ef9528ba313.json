{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst TestTubes = createLucideIcon(\"TestTubes\", [[\"path\", {\n  d: \"M9 2v17.5A2.5 2.5 0 0 1 6.5 22v0A2.5 2.5 0 0 1 4 19.5V2\",\n  key: \"12z67u\"\n}], [\"path\", {\n  d: \"M20 2v17.5a2.5 2.5 0 0 1-2.5 2.5v0a2.5 2.5 0 0 1-2.5-2.5V2\",\n  key: \"1q2nfy\"\n}], [\"path\", {\n  d: \"M3 2h7\",\n  key: \"7s29d5\"\n}], [\"path\", {\n  d: \"M14 2h7\",\n  key: \"7sicin\"\n}], [\"path\", {\n  d: \"M9 16H4\",\n  key: \"1bfye3\"\n}], [\"path\", {\n  d: \"M20 16h-5\",\n  key: \"ddnjpe\"\n}]]);\nexport { TestTubes as default };", "map": {"version": 3, "names": ["TestTubes", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\ClientApp\\node_modules\\lucide-react\\src\\icons\\test-tubes.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name TestTubes\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOSAydjE3LjVBMi41IDIuNSAwIDAgMSA2LjUgMjJ2MEEyLjUgMi41IDAgMCAxIDQgMTkuNVYyIiAvPgogIDxwYXRoIGQ9Ik0yMCAydjE3LjVhMi41IDIuNSAwIDAgMS0yLjUgMi41djBhMi41IDIuNSAwIDAgMS0yLjUtMi41VjIiIC8+CiAgPHBhdGggZD0iTTMgMmg3IiAvPgogIDxwYXRoIGQ9Ik0xNCAyaDciIC8+CiAgPHBhdGggZD0iTTkgMTZINCIgLz4KICA8cGF0aCBkPSJNMjAgMTZoLTUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/test-tubes\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TestTubes = createLucideIcon('TestTubes', [\n  ['path', { d: 'M9 2v17.5A2.5 2.5 0 0 1 6.5 22v0A2.5 2.5 0 0 1 4 19.5V2', key: '12z67u' }],\n  ['path', { d: 'M20 2v17.5a2.5 2.5 0 0 1-2.5 2.5v0a2.5 2.5 0 0 1-2.5-2.5V2', key: '1q2nfy' }],\n  ['path', { d: 'M3 2h7', key: '7s29d5' }],\n  ['path', { d: 'M14 2h7', key: '7sicin' }],\n  ['path', { d: 'M9 16H4', key: '1bfye3' }],\n  ['path', { d: 'M20 16h-5', key: 'ddnjpe' }],\n]);\n\nexport default TestTubes;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CAAC,MAAQ;EAAEC,CAAA,EAAG,yDAA2D;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxF,CAAC,MAAQ;EAAED,CAAA,EAAG,4DAA8D;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3F,CAAC,MAAQ;EAAED,CAAA,EAAG,QAAU;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}