@echo off
echo ========================================
echo   Technoloway React Landing Page Setup
echo ========================================
echo.

echo Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js 18+ from https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js version:
node --version

echo.
echo Checking npm installation...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: npm is not installed or not in PATH
    pause
    exit /b 1
)

echo npm version:
npm --version

echo.
echo Navigating to ClientApp directory...
cd ClientApp

echo.
echo Installing React dependencies...
echo This may take a few minutes...
npm install

if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo ========================================
echo   Setup Complete!
echo ========================================
echo.
echo To start the React development server:
echo   1. Open a new terminal/command prompt
echo   2. Navigate to: Technoloway.Web\ClientApp
echo   3. Run: npm start
echo.
echo To access the React landing page:
echo   1. Start your ASP.NET Core application
echo   2. Navigate to: https://localhost:7001/Home/React
echo.
echo For production build:
echo   Run: npm run build
echo.
pause
