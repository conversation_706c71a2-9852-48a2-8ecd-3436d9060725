import axios from 'axios';
import { Service, Project, Technology, Testimonial, ContactFormData, ContactFormResponse } from '../types';

const API_BASE_URL = '/api/chatbot';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// API service functions
export const apiService = {
  // Get all services
  getServices: async (): Promise<Service[]> => {
    const response = await api.get('/services');
    return response.data;
  },

  // Get projects (featured or all)
  getProjects: async (featuredOnly: boolean = false): Promise<Project[]> => {
    const response = await api.get(`/projects?featuredOnly=${featuredOnly}`);
    return response.data;
  },

  // Get technologies
  getTechnologies: async (): Promise<Technology[]> => {
    const response = await api.get('/technologies');
    return response.data;
  },

  // Get testimonials
  getTestimonials: async (): Promise<Testimonial[]> => {
    const response = await api.get('/testimonials');
    return response.data;
  },

  // Submit contact form
  submitContact: async (formData: ContactFormData): Promise<ContactFormResponse> => {
    // Transform the data to match the API expected format
    const apiData = {
      name: formData.name,
      email: formData.email,
      phone: formData.phone || '',
      contactMethod: 'Website Form',
      contactTime: 'Any time',
      company: '',
      projectType: formData.subject,
      budgetRange: '',
      timeline: '',
      message: formData.message
    };

    const response = await api.post('/contact', apiData);
    return response.data;
  },
};

// Error handling interceptor
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error);
    throw error;
  }
);
