{"ast": null, "code": "import { progress } from '../../../utils/progress.mjs';\nimport { calcLength } from '../../../projection/geometry/delta-calc.mjs';\nimport { clamp } from '../../../utils/clamp.mjs';\nimport { mix } from '../../../utils/mix.mjs';\n\n/**\n * Apply constraints to a point. These constraints are both physical along an\n * axis, and an elastic factor that determines how much to constrain the point\n * by if it does lie outside the defined parameters.\n */\nfunction applyConstraints(point, {\n  min,\n  max\n}, elastic) {\n  if (min !== undefined && point < min) {\n    // If we have a min point defined, and this is outside of that, constrain\n    point = elastic ? mix(min, point, elastic.min) : Math.max(point, min);\n  } else if (max !== undefined && point > max) {\n    // If we have a max point defined, and this is outside of that, constrain\n    point = elastic ? mix(max, point, elastic.max) : Math.min(point, max);\n  }\n  return point;\n}\n/**\n * Calculate constraints in terms of the viewport when defined relatively to the\n * measured axis. This is measured from the nearest edge, so a max constraint of 200\n * on an axis with a max value of 300 would return a constraint of 500 - axis length\n */\nfunction calcRelativeAxisConstraints(axis, min, max) {\n  return {\n    min: min !== undefined ? axis.min + min : undefined,\n    max: max !== undefined ? axis.max + max - (axis.max - axis.min) : undefined\n  };\n}\n/**\n * Calculate constraints in terms of the viewport when\n * defined relatively to the measured bounding box.\n */\nfunction calcRelativeConstraints(layoutBox, {\n  top,\n  left,\n  bottom,\n  right\n}) {\n  return {\n    x: calcRelativeAxisConstraints(layoutBox.x, left, right),\n    y: calcRelativeAxisConstraints(layoutBox.y, top, bottom)\n  };\n}\n/**\n * Calculate viewport constraints when defined as another viewport-relative axis\n */\nfunction calcViewportAxisConstraints(layoutAxis, constraintsAxis) {\n  let min = constraintsAxis.min - layoutAxis.min;\n  let max = constraintsAxis.max - layoutAxis.max;\n  // If the constraints axis is actually smaller than the layout axis then we can\n  // flip the constraints\n  if (constraintsAxis.max - constraintsAxis.min < layoutAxis.max - layoutAxis.min) {\n    [min, max] = [max, min];\n  }\n  return {\n    min,\n    max\n  };\n}\n/**\n * Calculate viewport constraints when defined as another viewport-relative box\n */\nfunction calcViewportConstraints(layoutBox, constraintsBox) {\n  return {\n    x: calcViewportAxisConstraints(layoutBox.x, constraintsBox.x),\n    y: calcViewportAxisConstraints(layoutBox.y, constraintsBox.y)\n  };\n}\n/**\n * Calculate a transform origin relative to the source axis, between 0-1, that results\n * in an asthetically pleasing scale/transform needed to project from source to target.\n */\nfunction calcOrigin(source, target) {\n  let origin = 0.5;\n  const sourceLength = calcLength(source);\n  const targetLength = calcLength(target);\n  if (targetLength > sourceLength) {\n    origin = progress(target.min, target.max - sourceLength, source.min);\n  } else if (sourceLength > targetLength) {\n    origin = progress(source.min, source.max - targetLength, target.min);\n  }\n  return clamp(0, 1, origin);\n}\n/**\n * Rebase the calculated viewport constraints relative to the layout.min point.\n */\nfunction rebaseAxisConstraints(layout, constraints) {\n  const relativeConstraints = {};\n  if (constraints.min !== undefined) {\n    relativeConstraints.min = constraints.min - layout.min;\n  }\n  if (constraints.max !== undefined) {\n    relativeConstraints.max = constraints.max - layout.min;\n  }\n  return relativeConstraints;\n}\nconst defaultElastic = 0.35;\n/**\n * Accepts a dragElastic prop and returns resolved elastic values for each axis.\n */\nfunction resolveDragElastic(dragElastic = defaultElastic) {\n  if (dragElastic === false) {\n    dragElastic = 0;\n  } else if (dragElastic === true) {\n    dragElastic = defaultElastic;\n  }\n  return {\n    x: resolveAxisElastic(dragElastic, \"left\", \"right\"),\n    y: resolveAxisElastic(dragElastic, \"top\", \"bottom\")\n  };\n}\nfunction resolveAxisElastic(dragElastic, minLabel, maxLabel) {\n  return {\n    min: resolvePointElastic(dragElastic, minLabel),\n    max: resolvePointElastic(dragElastic, maxLabel)\n  };\n}\nfunction resolvePointElastic(dragElastic, label) {\n  return typeof dragElastic === \"number\" ? dragElastic : dragElastic[label] || 0;\n}\nexport { applyConstraints, calcOrigin, calcRelativeAxisConstraints, calcRelativeConstraints, calcViewportAxisConstraints, calcViewportConstraints, defaultElastic, rebaseAxisConstraints, resolveAxisElastic, resolveDragElastic, resolvePointElastic };", "map": {"version": 3, "names": ["progress", "calcLength", "clamp", "mix", "applyConstraints", "point", "min", "max", "elastic", "undefined", "Math", "calcRelativeAxisConstraints", "axis", "calcRelativeConstraints", "layoutBox", "top", "left", "bottom", "right", "x", "y", "calcViewportAxisConstraints", "layoutAxis", "constraintsAxis", "calcViewportConstraints", "constraintsBox", "calcOrigin", "source", "target", "origin", "sourceLength", "targetLength", "rebaseAxisConstraints", "layout", "constraints", "relativeConstraints", "defaultElastic", "resolveDragElastic", "dragElastic", "resolveAxisElastic", "minLabel", "max<PERSON><PERSON><PERSON>", "resolvePointElastic", "label"], "sources": ["C:/Users/<USER>/Documents/Project By AI/Technoloway/Technoloway (Processing)/Technoloway.Web/ClientApp/node_modules/framer-motion/dist/es/gestures/drag/utils/constraints.mjs"], "sourcesContent": ["import { progress } from '../../../utils/progress.mjs';\nimport { calcLength } from '../../../projection/geometry/delta-calc.mjs';\nimport { clamp } from '../../../utils/clamp.mjs';\nimport { mix } from '../../../utils/mix.mjs';\n\n/**\n * Apply constraints to a point. These constraints are both physical along an\n * axis, and an elastic factor that determines how much to constrain the point\n * by if it does lie outside the defined parameters.\n */\nfunction applyConstraints(point, { min, max }, elastic) {\n    if (min !== undefined && point < min) {\n        // If we have a min point defined, and this is outside of that, constrain\n        point = elastic ? mix(min, point, elastic.min) : Math.max(point, min);\n    }\n    else if (max !== undefined && point > max) {\n        // If we have a max point defined, and this is outside of that, constrain\n        point = elastic ? mix(max, point, elastic.max) : Math.min(point, max);\n    }\n    return point;\n}\n/**\n * Calculate constraints in terms of the viewport when defined relatively to the\n * measured axis. This is measured from the nearest edge, so a max constraint of 200\n * on an axis with a max value of 300 would return a constraint of 500 - axis length\n */\nfunction calcRelativeAxisConstraints(axis, min, max) {\n    return {\n        min: min !== undefined ? axis.min + min : undefined,\n        max: max !== undefined\n            ? axis.max + max - (axis.max - axis.min)\n            : undefined,\n    };\n}\n/**\n * Calculate constraints in terms of the viewport when\n * defined relatively to the measured bounding box.\n */\nfunction calcRelativeConstraints(layoutBox, { top, left, bottom, right }) {\n    return {\n        x: calcRelativeAxisConstraints(layoutBox.x, left, right),\n        y: calcRelativeAxisConstraints(layoutBox.y, top, bottom),\n    };\n}\n/**\n * Calculate viewport constraints when defined as another viewport-relative axis\n */\nfunction calcViewportAxisConstraints(layoutAxis, constraintsAxis) {\n    let min = constraintsAxis.min - layoutAxis.min;\n    let max = constraintsAxis.max - layoutAxis.max;\n    // If the constraints axis is actually smaller than the layout axis then we can\n    // flip the constraints\n    if (constraintsAxis.max - constraintsAxis.min <\n        layoutAxis.max - layoutAxis.min) {\n        [min, max] = [max, min];\n    }\n    return { min, max };\n}\n/**\n * Calculate viewport constraints when defined as another viewport-relative box\n */\nfunction calcViewportConstraints(layoutBox, constraintsBox) {\n    return {\n        x: calcViewportAxisConstraints(layoutBox.x, constraintsBox.x),\n        y: calcViewportAxisConstraints(layoutBox.y, constraintsBox.y),\n    };\n}\n/**\n * Calculate a transform origin relative to the source axis, between 0-1, that results\n * in an asthetically pleasing scale/transform needed to project from source to target.\n */\nfunction calcOrigin(source, target) {\n    let origin = 0.5;\n    const sourceLength = calcLength(source);\n    const targetLength = calcLength(target);\n    if (targetLength > sourceLength) {\n        origin = progress(target.min, target.max - sourceLength, source.min);\n    }\n    else if (sourceLength > targetLength) {\n        origin = progress(source.min, source.max - targetLength, target.min);\n    }\n    return clamp(0, 1, origin);\n}\n/**\n * Rebase the calculated viewport constraints relative to the layout.min point.\n */\nfunction rebaseAxisConstraints(layout, constraints) {\n    const relativeConstraints = {};\n    if (constraints.min !== undefined) {\n        relativeConstraints.min = constraints.min - layout.min;\n    }\n    if (constraints.max !== undefined) {\n        relativeConstraints.max = constraints.max - layout.min;\n    }\n    return relativeConstraints;\n}\nconst defaultElastic = 0.35;\n/**\n * Accepts a dragElastic prop and returns resolved elastic values for each axis.\n */\nfunction resolveDragElastic(dragElastic = defaultElastic) {\n    if (dragElastic === false) {\n        dragElastic = 0;\n    }\n    else if (dragElastic === true) {\n        dragElastic = defaultElastic;\n    }\n    return {\n        x: resolveAxisElastic(dragElastic, \"left\", \"right\"),\n        y: resolveAxisElastic(dragElastic, \"top\", \"bottom\"),\n    };\n}\nfunction resolveAxisElastic(dragElastic, minLabel, maxLabel) {\n    return {\n        min: resolvePointElastic(dragElastic, minLabel),\n        max: resolvePointElastic(dragElastic, maxLabel),\n    };\n}\nfunction resolvePointElastic(dragElastic, label) {\n    return typeof dragElastic === \"number\"\n        ? dragElastic\n        : dragElastic[label] || 0;\n}\n\nexport { applyConstraints, calcOrigin, calcRelativeAxisConstraints, calcRelativeConstraints, calcViewportAxisConstraints, calcViewportConstraints, defaultElastic, rebaseAxisConstraints, resolveAxisElastic, resolveDragElastic, resolvePointElastic };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,6BAA6B;AACtD,SAASC,UAAU,QAAQ,6CAA6C;AACxE,SAASC,KAAK,QAAQ,0BAA0B;AAChD,SAASC,GAAG,QAAQ,wBAAwB;;AAE5C;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EAAEC,GAAG;EAAEC;AAAI,CAAC,EAAEC,OAAO,EAAE;EACpD,IAAIF,GAAG,KAAKG,SAAS,IAAIJ,KAAK,GAAGC,GAAG,EAAE;IAClC;IACAD,KAAK,GAAGG,OAAO,GAAGL,GAAG,CAACG,GAAG,EAAED,KAAK,EAAEG,OAAO,CAACF,GAAG,CAAC,GAAGI,IAAI,CAACH,GAAG,CAACF,KAAK,EAAEC,GAAG,CAAC;EACzE,CAAC,MACI,IAAIC,GAAG,KAAKE,SAAS,IAAIJ,KAAK,GAAGE,GAAG,EAAE;IACvC;IACAF,KAAK,GAAGG,OAAO,GAAGL,GAAG,CAACI,GAAG,EAAEF,KAAK,EAAEG,OAAO,CAACD,GAAG,CAAC,GAAGG,IAAI,CAACJ,GAAG,CAACD,KAAK,EAAEE,GAAG,CAAC;EACzE;EACA,OAAOF,KAAK;AAChB;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,2BAA2BA,CAACC,IAAI,EAAEN,GAAG,EAAEC,GAAG,EAAE;EACjD,OAAO;IACHD,GAAG,EAAEA,GAAG,KAAKG,SAAS,GAAGG,IAAI,CAACN,GAAG,GAAGA,GAAG,GAAGG,SAAS;IACnDF,GAAG,EAAEA,GAAG,KAAKE,SAAS,GAChBG,IAAI,CAACL,GAAG,GAAGA,GAAG,IAAIK,IAAI,CAACL,GAAG,GAAGK,IAAI,CAACN,GAAG,CAAC,GACtCG;EACV,CAAC;AACL;AACA;AACA;AACA;AACA;AACA,SAASI,uBAAuBA,CAACC,SAAS,EAAE;EAAEC,GAAG;EAAEC,IAAI;EAAEC,MAAM;EAAEC;AAAM,CAAC,EAAE;EACtE,OAAO;IACHC,CAAC,EAAER,2BAA2B,CAACG,SAAS,CAACK,CAAC,EAAEH,IAAI,EAAEE,KAAK,CAAC;IACxDE,CAAC,EAAET,2BAA2B,CAACG,SAAS,CAACM,CAAC,EAAEL,GAAG,EAAEE,MAAM;EAC3D,CAAC;AACL;AACA;AACA;AACA;AACA,SAASI,2BAA2BA,CAACC,UAAU,EAAEC,eAAe,EAAE;EAC9D,IAAIjB,GAAG,GAAGiB,eAAe,CAACjB,GAAG,GAAGgB,UAAU,CAAChB,GAAG;EAC9C,IAAIC,GAAG,GAAGgB,eAAe,CAAChB,GAAG,GAAGe,UAAU,CAACf,GAAG;EAC9C;EACA;EACA,IAAIgB,eAAe,CAAChB,GAAG,GAAGgB,eAAe,CAACjB,GAAG,GACzCgB,UAAU,CAACf,GAAG,GAAGe,UAAU,CAAChB,GAAG,EAAE;IACjC,CAACA,GAAG,EAAEC,GAAG,CAAC,GAAG,CAACA,GAAG,EAAED,GAAG,CAAC;EAC3B;EACA,OAAO;IAAEA,GAAG;IAAEC;EAAI,CAAC;AACvB;AACA;AACA;AACA;AACA,SAASiB,uBAAuBA,CAACV,SAAS,EAAEW,cAAc,EAAE;EACxD,OAAO;IACHN,CAAC,EAAEE,2BAA2B,CAACP,SAAS,CAACK,CAAC,EAAEM,cAAc,CAACN,CAAC,CAAC;IAC7DC,CAAC,EAAEC,2BAA2B,CAACP,SAAS,CAACM,CAAC,EAAEK,cAAc,CAACL,CAAC;EAChE,CAAC;AACL;AACA;AACA;AACA;AACA;AACA,SAASM,UAAUA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAChC,IAAIC,MAAM,GAAG,GAAG;EAChB,MAAMC,YAAY,GAAG7B,UAAU,CAAC0B,MAAM,CAAC;EACvC,MAAMI,YAAY,GAAG9B,UAAU,CAAC2B,MAAM,CAAC;EACvC,IAAIG,YAAY,GAAGD,YAAY,EAAE;IAC7BD,MAAM,GAAG7B,QAAQ,CAAC4B,MAAM,CAACtB,GAAG,EAAEsB,MAAM,CAACrB,GAAG,GAAGuB,YAAY,EAAEH,MAAM,CAACrB,GAAG,CAAC;EACxE,CAAC,MACI,IAAIwB,YAAY,GAAGC,YAAY,EAAE;IAClCF,MAAM,GAAG7B,QAAQ,CAAC2B,MAAM,CAACrB,GAAG,EAAEqB,MAAM,CAACpB,GAAG,GAAGwB,YAAY,EAAEH,MAAM,CAACtB,GAAG,CAAC;EACxE;EACA,OAAOJ,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE2B,MAAM,CAAC;AAC9B;AACA;AACA;AACA;AACA,SAASG,qBAAqBA,CAACC,MAAM,EAAEC,WAAW,EAAE;EAChD,MAAMC,mBAAmB,GAAG,CAAC,CAAC;EAC9B,IAAID,WAAW,CAAC5B,GAAG,KAAKG,SAAS,EAAE;IAC/B0B,mBAAmB,CAAC7B,GAAG,GAAG4B,WAAW,CAAC5B,GAAG,GAAG2B,MAAM,CAAC3B,GAAG;EAC1D;EACA,IAAI4B,WAAW,CAAC3B,GAAG,KAAKE,SAAS,EAAE;IAC/B0B,mBAAmB,CAAC5B,GAAG,GAAG2B,WAAW,CAAC3B,GAAG,GAAG0B,MAAM,CAAC3B,GAAG;EAC1D;EACA,OAAO6B,mBAAmB;AAC9B;AACA,MAAMC,cAAc,GAAG,IAAI;AAC3B;AACA;AACA;AACA,SAASC,kBAAkBA,CAACC,WAAW,GAAGF,cAAc,EAAE;EACtD,IAAIE,WAAW,KAAK,KAAK,EAAE;IACvBA,WAAW,GAAG,CAAC;EACnB,CAAC,MACI,IAAIA,WAAW,KAAK,IAAI,EAAE;IAC3BA,WAAW,GAAGF,cAAc;EAChC;EACA,OAAO;IACHjB,CAAC,EAAEoB,kBAAkB,CAACD,WAAW,EAAE,MAAM,EAAE,OAAO,CAAC;IACnDlB,CAAC,EAAEmB,kBAAkB,CAACD,WAAW,EAAE,KAAK,EAAE,QAAQ;EACtD,CAAC;AACL;AACA,SAASC,kBAAkBA,CAACD,WAAW,EAAEE,QAAQ,EAAEC,QAAQ,EAAE;EACzD,OAAO;IACHnC,GAAG,EAAEoC,mBAAmB,CAACJ,WAAW,EAAEE,QAAQ,CAAC;IAC/CjC,GAAG,EAAEmC,mBAAmB,CAACJ,WAAW,EAAEG,QAAQ;EAClD,CAAC;AACL;AACA,SAASC,mBAAmBA,CAACJ,WAAW,EAAEK,KAAK,EAAE;EAC7C,OAAO,OAAOL,WAAW,KAAK,QAAQ,GAChCA,WAAW,GACXA,WAAW,CAACK,KAAK,CAAC,IAAI,CAAC;AACjC;AAEA,SAASvC,gBAAgB,EAAEsB,UAAU,EAAEf,2BAA2B,EAAEE,uBAAuB,EAAEQ,2BAA2B,EAAEG,uBAAuB,EAAEY,cAAc,EAAEJ,qBAAqB,EAAEO,kBAAkB,EAAEF,kBAAkB,EAAEK,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}