using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Technoloway.Core.Entities;
using Technoloway.Core.Interfaces;

namespace Technoloway.Web.Controllers.Api
{
    [ApiController]
    [Route("api/[controller]")]
    public class LandingPageController : ControllerBase
    {
        private readonly IRepository<Service> _serviceRepository;
        private readonly IRepository<Project> _projectRepository;
        private readonly IRepository<Technology> _technologyRepository;
        private readonly IRepository<Testimonial> _testimonialRepository;
        private readonly IRepository<ContactForm> _contactFormRepository;

        public LandingPageController(
            IRepository<Service> serviceRepository,
            IRepository<Project> projectRepository,
            IRepository<Technology> technologyRepository,
            IRepository<Testimonial> testimonialRepository,
            IRepository<ContactForm> contactFormRepository)
        {
            _serviceRepository = serviceRepository;
            _projectRepository = projectRepository;
            _technologyRepository = technologyRepository;
            _testimonialRepository = testimonialRepository;
            _contactFormRepository = contactFormRepository;
        }

        [HttpGet("services")]
        public async Task<IActionResult> GetServices()
        {
            try
            {
                var services = await _serviceRepository.ListAsync(s => s.IsActive);
                var serviceData = services.OrderBy(s => s.DisplayOrder).Select(s => new
                {
                    s.Id,
                    s.Name,
                    s.Description,
                    s.IconClass,
                    s.Price,
                    ProjectCount = s.Projects?.Count ?? 0
                }).ToList();

                return Ok(serviceData);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "Failed to fetch services", details = ex.Message });
            }
        }

        [HttpGet("projects")]
        public async Task<IActionResult> GetProjects([FromQuery] bool featuredOnly = false)
        {
            try
            {
                var projects = featuredOnly 
                    ? await _projectRepository.ListAsync(p => p.IsFeatured)
                    : await _projectRepository.GetAll()
                        .Include(p => p.Order)
                        .Include(p => p.Technologies)
                        .Where(p => !p.IsDeleted)
                        .OrderByDescending(p => p.ProjCompletionDate)
                        .Take(6)
                        .ToListAsync();

                var projectData = projects.Select(p => new
                {
                    p.Id,
                    p.Name,
                    p.Description,
                    p.ClientName,
                    p.ImageUrl,
                    p.ProjectUrl,
                    ProjCompletionDate = p.ProjCompletionDate,
                    p.IsFeatured,
                    Order = p.Order?.OrderTitle,
                    Technologies = p.Technologies?.Select(t => t.Name).ToList() ?? new List<string>()
                }).ToList();

                return Ok(projectData);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "Failed to fetch projects", details = ex.Message });
            }
        }

        [HttpGet("technologies")]
        public async Task<IActionResult> GetTechnologies()
        {
            try
            {
                var technologies = await _technologyRepository.GetAll()
                    .Where(t => !t.IsDeleted)
                    .OrderBy(t => t.DisplayOrder)
                    .ToListAsync();

                var techData = technologies.Select(t => new
                {
                    t.Id,
                    t.Name,
                    t.Description,
                    t.IconUrl,
                    ProjectCount = t.Projects?.Count ?? 0
                }).ToList();

                return Ok(techData);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "Failed to fetch technologies", details = ex.Message });
            }
        }

        [HttpGet("testimonials")]
        public async Task<IActionResult> GetTestimonials()
        {
            try
            {
                var testimonials = await _testimonialRepository.ListAsync(t => t.IsActive);
                var testimonialData = testimonials.OrderBy(t => t.DisplayOrder).Select(t => new
                {
                    t.Id,
                    t.ClientName,
                    t.ClientCompany,
                    t.ClientTitle,
                    t.Content,
                    t.Rating,
                    t.ClientPhotoUrl
                }).ToList();

                return Ok(testimonialData);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "Failed to fetch testimonials", details = ex.Message });
            }
        }

        [HttpPost("contact")]
        public async Task<IActionResult> SubmitContact([FromBody] ContactFormRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var contactForm = new ContactForm
                {
                    Name = request.Name,
                    Email = request.Email,
                    Phone = request.Phone ?? string.Empty,
                    Subject = request.Subject,
                    Message = request.Message,
                    IsRead = false,
                    Status = "New",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                await _contactFormRepository.AddAsync(contactForm);

                return Ok(new { success = true, message = "Your message has been sent successfully. We'll get back to you soon!" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "Failed to submit contact form", details = ex.Message });
            }
        }
    }

    public class ContactFormRequest
    {
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string? Phone { get; set; }
        public string Subject { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
    }
}
