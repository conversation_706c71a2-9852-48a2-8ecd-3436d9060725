{"ast": null, "code": "/*\n  Convert velocity into velocity per second\n\n  @param [number]: Unit per frame\n  @param [number]: Frame duration in ms\n*/\nfunction velocityPerSecond(velocity, frameDuration) {\n  return frameDuration ? velocity * (1000 / frameDuration) : 0;\n}\nexport { velocityPerSecond };", "map": {"version": 3, "names": ["velocityPerSecond", "velocity", "frameDuration"], "sources": ["C:/Users/<USER>/Documents/Project By AI/Technoloway/Technoloway (Processing)/Technoloway.Web/ClientApp/node_modules/framer-motion/dist/es/utils/velocity-per-second.mjs"], "sourcesContent": ["/*\n  Convert velocity into velocity per second\n\n  @param [number]: Unit per frame\n  @param [number]: Frame duration in ms\n*/\nfunction velocityPerSecond(velocity, frameDuration) {\n    return frameDuration ? velocity * (1000 / frameDuration) : 0;\n}\n\nexport { velocityPerSecond };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,iBAAiBA,CAACC,QAAQ,EAAEC,aAAa,EAAE;EAChD,OAAOA,aAAa,GAAGD,QAAQ,IAAI,IAAI,GAAGC,aAAa,CAAC,GAAG,CAAC;AAChE;AAEA,SAASF,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}