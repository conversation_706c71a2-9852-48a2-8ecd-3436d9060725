@{
    ViewData["Title"] = "Technoloway - Innovative Software Solutions";
    ViewData["MetaDescription"] = "Technoloway - Innovative Software Solutions for Your Business. Transform your business with cutting-edge software development services.";
    ViewData["MetaKeywords"] = "software development, web development, mobile apps, cloud solutions, IT services, React, modern web applications";
    Layout = "_ReactLayout";
}

<div id="react-landing-page">
    <!-- React app will be mounted here -->
    <div class="loading-container" style="display: flex; justify-content: center; align-items: center; min-height: 100vh; background: linear-gradient(135deg, #1e293b, #4f46e5);">
        <div style="text-align: center; color: white;">
            <div class="spinner" style="border: 4px solid #f3f4f6; border-top: 4px solid #4f46e5; border-radius: 50%; width: 40px; height: 40px; animation: spin 1s linear infinite; margin: 0 auto 20px;"></div>
            <p style="font-family: 'Inter', sans-serif; font-size: 18px; margin: 0;">Loading Technoloway...</p>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Remove loading screen when React app loads
        window.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                const loadingContainer = document.querySelector('.loading-container');
                if (loadingContainer) {
                    loadingContainer.style.display = 'none';
                }
            }, 2000);
        });
    </script>
}

<style>
    @@keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
