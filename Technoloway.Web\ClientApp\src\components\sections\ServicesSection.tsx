import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { ArrowRight, Grid3X3 } from 'lucide-react';
import AnimatedSection from '../ui/AnimatedSection';
import Button from '../ui/Button';
import LoadingSpinner from '../ui/LoadingSpinner';
import { Service } from '../../types';
import { apiService } from '../../services/api';

const ServicesSection: React.FC = () => {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchServices = async () => {
      try {
        const data = await apiService.getServices();
        setServices(data.slice(0, 8)); // Show first 8 services
      } catch (err) {
        setError('Failed to load services');
        console.error('Error fetching services:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchServices();
  }, []);

  const handleServiceClick = (serviceId: number) => {
    // Navigate to service details - you can implement this based on your routing
    console.log('Navigate to service:', serviceId);
  };

  const handleViewAllServices = () => {
    // Navigate to services page
    window.location.href = '/services';
  };

  if (loading) {
    return (
      <section id="services" className="py-24 bg-gray-50">
        <div className="container mx-auto px-6">
          <div className="flex justify-center">
            <LoadingSpinner size="lg" />
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section id="services" className="py-24 bg-gray-50">
        <div className="container mx-auto px-6">
          <div className="text-center text-red-600">{error}</div>
        </div>
      </section>
    );
  }

  return (
    <section id="services" className="py-24 bg-gray-50">
      <div className="container mx-auto px-6">
        {/* Section Header */}
        <AnimatedSection className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-secondary-900 mb-6">
            <span className="bg-gradient-to-r from-primary-600 to-accent-500 bg-clip-text text-transparent">
              Our
            </span>{' '}
            Services
          </h2>
          <p className="text-xl text-secondary-600 max-w-3xl mx-auto leading-relaxed">
            We offer a comprehensive range of software development services designed to transform your business and drive digital innovation.
          </p>
        </AnimatedSection>

        {/* Services Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {services.map((service, index) => (
            <AnimatedSection key={service.id} delay={index + 1}>
              <motion.div
                className="bg-white rounded-2xl p-8 shadow-modern hover:shadow-modern-lg transition-all duration-300 cursor-pointer group h-full"
                whileHover={{ y: -8 }}
                onClick={() => handleServiceClick(service.id)}
              >
                {/* Service Icon */}
                <div className="relative mb-6">
                  <div className="w-16 h-16 bg-gradient-to-r from-primary-500 to-accent-500 rounded-xl flex items-center justify-center text-white text-2xl mb-4 group-hover:scale-110 transition-transform duration-300">
                    {service.iconClass?.startsWith('/images/') ? (
                      <img 
                        src={service.iconClass} 
                        alt={`${service.name} Icon`} 
                        className="w-8 h-8 object-contain"
                      />
                    ) : (
                      <i className={service.iconClass || 'fas fa-cog'}></i>
                    )}
                  </div>
                  
                  {/* Hover overlay */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-primary-500/10 to-accent-500/10 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                    initial={false}
                  />
                </div>

                {/* Service Content */}
                <h3 className="text-xl font-bold text-secondary-900 mb-3 group-hover:text-primary-600 transition-colors duration-300">
                  {service.name}
                </h3>
                
                <p className="text-secondary-600 mb-4 line-clamp-3">
                  {service.description}
                </p>

                {/* Service Meta */}
                <div className="flex items-center justify-between text-sm text-secondary-500 mb-4">
                  <span>{service.projectCount} Projects</span>
                  <span className="font-semibold text-primary-600">
                    ${service.price.toLocaleString()}
                  </span>
                </div>

                {/* Learn More Button */}
                <div className="flex items-center text-primary-600 font-semibold group-hover:text-primary-700 transition-colors duration-300">
                  <span className="mr-2">Learn More</span>
                  <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
                </div>
              </motion.div>
            </AnimatedSection>
          ))}
        </div>

        {/* View All Services Button */}
        <AnimatedSection delay={5} className="text-center">
          <Button
            variant="primary"
            size="lg"
            onClick={handleViewAllServices}
            icon={<Grid3X3 className="w-5 h-5" />}
          >
            View All Services
          </Button>
        </AnimatedSection>
      </div>
    </section>
  );
};

export default ServicesSection;
