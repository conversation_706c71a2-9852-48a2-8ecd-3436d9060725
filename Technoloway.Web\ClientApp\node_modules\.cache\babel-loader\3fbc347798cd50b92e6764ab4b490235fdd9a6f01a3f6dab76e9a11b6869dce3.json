{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst View = createLucideIcon(\"View\", [[\"path\", {\n  d: \"M5 12s2.545-5 7-5c4.454 0 7 5 7 5s-2.546 5-7 5c-4.455 0-7-5-7-5z\",\n  key: \"vptub8\"\n}], [\"path\", {\n  d: \"M12 13a1 1 0 1 0 0-2 1 1 0 0 0 0 2z\",\n  key: \"10lhjs\"\n}], [\"path\", {\n  d: \"M21 17v2a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-2\",\n  key: \"mrq65r\"\n}], [\"path\", {\n  d: \"M21 7V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v2\",\n  key: \"be3xqs\"\n}]]);\nexport { View as default };", "map": {"version": 3, "names": ["View", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\ClientApp\\node_modules\\lucide-react\\src\\icons\\view.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name View\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMnMyLjU0NS01IDctNWM0LjQ1NCAwIDcgNSA3IDVzLTIuNTQ2IDUtNyA1Yy00LjQ1NSAwLTctNS03LTV6IiAvPgogIDxwYXRoIGQ9Ik0xMiAxM2ExIDEgMCAxIDAgMC0yIDEgMSAwIDAgMCAwIDJ6IiAvPgogIDxwYXRoIGQ9Ik0yMSAxN3YyYTIgMiAwIDAgMS0yIDJINWEyIDIgMCAwIDEtMi0ydi0yIiAvPgogIDxwYXRoIGQ9Ik0yMSA3VjVhMiAyIDAgMCAwLTItMkg1YTIgMiAwIDAgMC0yIDJ2MiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/view\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst View = createLucideIcon('View', [\n  [\n    'path',\n    { d: 'M5 12s2.545-5 7-5c4.454 0 7 5 7 5s-2.546 5-7 5c-4.455 0-7-5-7-5z', key: 'vptub8' },\n  ],\n  ['path', { d: 'M12 13a1 1 0 1 0 0-2 1 1 0 0 0 0 2z', key: '10lhjs' }],\n  ['path', { d: 'M21 17v2a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-2', key: 'mrq65r' }],\n  ['path', { d: 'M21 7V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v2', key: 'be3xqs' }],\n]);\n\nexport default View;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,IAAA,GAAOC,gBAAA,CAAiB,MAAQ,GACpC,CACE,QACA;EAAEC,CAAA,EAAG,kEAAoE;EAAAC,GAAA,EAAK;AAAS,EACzF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,qCAAuC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpE,CAAC,MAAQ;EAAED,CAAA,EAAG,2CAA6C;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1E,CAAC,MAAQ;EAAED,CAAA,EAAG,yCAA2C;EAAAC,GAAA,EAAK;AAAA,CAAU,EACzE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}