{"ast": null, "code": "const isKeyframesTarget = v => {\n  return Array.isArray(v);\n};\nexport { isKeyframesTarget };", "map": {"version": 3, "names": ["isKeyframesTarget", "v", "Array", "isArray"], "sources": ["C:/Users/<USER>/Documents/Project By AI/Technoloway/Technoloway (Processing)/Technoloway.Web/ClientApp/node_modules/framer-motion/dist/es/animation/utils/is-keyframes-target.mjs"], "sourcesContent": ["const isKeyframesTarget = (v) => {\n    return Array.isArray(v);\n};\n\nexport { isKeyframesTarget };\n"], "mappings": "AAAA,MAAMA,iBAAiB,GAAIC,CAAC,IAAK;EAC7B,OAAOC,KAAK,CAACC,OAAO,CAACF,CAAC,CAAC;AAC3B,CAAC;AAED,SAASD,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}