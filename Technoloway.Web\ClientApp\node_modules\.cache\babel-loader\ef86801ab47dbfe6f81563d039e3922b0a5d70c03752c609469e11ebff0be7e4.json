{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst SignalZero = createLucideIcon(\"SignalZero\", [[\"path\", {\n  d: \"M2 20h.01\",\n  key: \"4haj6o\"\n}]]);\nexport { SignalZero as default };", "map": {"version": 3, "names": ["SignalZero", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\ClientApp\\node_modules\\lucide-react\\src\\icons\\signal-zero.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name SignalZero\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMiAyMGguMDEiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/signal-zero\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst SignalZero = createLucideIcon('SignalZero', [['path', { d: 'M2 20h.01', key: '4haj6o' }]]);\n\nexport default SignalZero;\n"], "mappings": ";;;;;;;;AAaA,MAAMA,UAAa,GAAAC,gBAAA,CAAiB,YAAc,GAAC,CAAC,QAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAS,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}