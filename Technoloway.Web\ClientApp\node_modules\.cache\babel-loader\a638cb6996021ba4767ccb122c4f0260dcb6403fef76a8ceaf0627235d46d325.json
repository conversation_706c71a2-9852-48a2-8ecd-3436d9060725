{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst WrapText = createLucideIcon(\"WrapText\", [[\"line\", {\n  x1: \"3\",\n  x2: \"21\",\n  y1: \"6\",\n  y2: \"6\",\n  key: \"4m8b97\"\n}], [\"path\", {\n  d: \"M3 12h15a3 3 0 1 1 0 6h-4\",\n  key: \"1cl7v7\"\n}], [\"polyline\", {\n  points: \"16 16 14 18 16 20\",\n  key: \"1jznyi\"\n}], [\"line\", {\n  x1: \"3\",\n  x2: \"10\",\n  y1: \"18\",\n  y2: \"18\",\n  key: \"1h33wv\"\n}]]);\nexport { WrapText as default };", "map": {"version": 3, "names": ["WrapText", "createLucideIcon", "x1", "x2", "y1", "y2", "key", "d", "points"], "sources": ["C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\ClientApp\\node_modules\\lucide-react\\src\\icons\\wrap-text.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name WrapText\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iMyIgeDI9IjIxIiB5MT0iNiIgeTI9IjYiIC8+CiAgPHBhdGggZD0iTTMgMTJoMTVhMyAzIDAgMSAxIDAgNmgtNCIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSIxNiAxNiAxNCAxOCAxNiAyMCIgLz4KICA8bGluZSB4MT0iMyIgeDI9IjEwIiB5MT0iMTgiIHkyPSIxOCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/wrap-text\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst WrapText = createLucideIcon('WrapText', [\n  ['line', { x1: '3', x2: '21', y1: '6', y2: '6', key: '4m8b97' }],\n  ['path', { d: 'M3 12h15a3 3 0 1 1 0 6h-4', key: '1cl7v7' }],\n  ['polyline', { points: '16 16 14 18 16 20', key: '1jznyi' }],\n  ['line', { x1: '3', x2: '10', y1: '18', y2: '18', key: '1h33wv' }],\n]);\n\nexport default WrapText;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/D,CAAC,MAAQ;EAAEC,CAAA,EAAG,2BAA6B;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC1D,CAAC,UAAY;EAAEE,MAAA,EAAQ,mBAAqB;EAAAF,GAAA,EAAK;AAAA,CAAU,GAC3D,CAAC,QAAQ;EAAEJ,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,GAAA,EAAK;AAAA,CAAU,EAClE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}