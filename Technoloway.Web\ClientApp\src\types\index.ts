// API Response Types
export interface Service {
  id: number;
  name: string;
  description: string;
  iconClass: string;
  price: number;
  projectCount: number;
}

export interface Project {
  id: number;
  name: string;
  description: string;
  clientName: string;
  imageUrl: string;
  projectUrl: string;
  projCompletionDate: string;
  isFeatured: boolean;
  order: string;
  technologies: string[];
}

export interface Technology {
  id: number;
  name: string;
  description: string;
  iconUrl: string;
  projectCount: number;
}

export interface Testimonial {
  id: number;
  clientName: string;
  clientCompany: string;
  clientTitle: string;
  content: string;
  rating: number;
  clientPhotoUrl: string;
}

export interface ContactFormData {
  name: string;
  email: string;
  phone?: string;
  subject: string;
  message: string;
}

export interface ContactFormResponse {
  success: boolean;
  message: string;
}

// Component Props Types
export interface AnimatedSectionProps {
  children: React.ReactNode;
  className?: string;
  delay?: number;
}

export interface ButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  disabled?: boolean;
  className?: string;
  icon?: React.ReactNode;
}
