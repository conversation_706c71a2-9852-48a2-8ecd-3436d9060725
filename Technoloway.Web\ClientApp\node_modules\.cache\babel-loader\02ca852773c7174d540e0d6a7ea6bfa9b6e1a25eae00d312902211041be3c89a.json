{"ast": null, "code": "import { mapEasingToNativeEasing } from './easing.mjs';\nfunction animateStyle(element, valueName, keyframes, {\n  delay = 0,\n  duration,\n  repeat = 0,\n  repeatType = \"loop\",\n  ease,\n  times\n} = {}) {\n  const keyframeOptions = {\n    [valueName]: keyframes\n  };\n  if (times) keyframeOptions.offset = times;\n  const easing = mapEasingToNativeEasing(ease);\n  /**\n   * If this is an easing array, apply to keyframes, not animation as a whole\n   */\n  if (Array.isArray(easing)) keyframeOptions.easing = easing;\n  return element.animate(keyframeOptions, {\n    delay,\n    duration,\n    easing: !Array.isArray(easing) ? easing : \"linear\",\n    fill: \"both\",\n    iterations: repeat + 1,\n    direction: repeatType === \"reverse\" ? \"alternate\" : \"normal\"\n  });\n}\nexport { animateStyle };", "map": {"version": 3, "names": ["mapEasingToNativeEasing", "animateStyle", "element", "valueName", "keyframes", "delay", "duration", "repeat", "repeatType", "ease", "times", "keyframeOptions", "offset", "easing", "Array", "isArray", "animate", "fill", "iterations", "direction"], "sources": ["C:/Users/<USER>/Documents/Project By AI/Technoloway/Technoloway (Processing)/Technoloway.Web/ClientApp/node_modules/framer-motion/dist/es/animation/animators/waapi/index.mjs"], "sourcesContent": ["import { mapEasingToNativeEasing } from './easing.mjs';\n\nfunction animateStyle(element, valueName, keyframes, { delay = 0, duration, repeat = 0, repeatType = \"loop\", ease, times, } = {}) {\n    const keyframeOptions = { [valueName]: keyframes };\n    if (times)\n        keyframeOptions.offset = times;\n    const easing = mapEasingToNativeEasing(ease);\n    /**\n     * If this is an easing array, apply to keyframes, not animation as a whole\n     */\n    if (Array.isArray(easing))\n        keyframeOptions.easing = easing;\n    return element.animate(keyframeOptions, {\n        delay,\n        duration,\n        easing: !Array.isArray(easing) ? easing : \"linear\",\n        fill: \"both\",\n        iterations: repeat + 1,\n        direction: repeatType === \"reverse\" ? \"alternate\" : \"normal\",\n    });\n}\n\nexport { animateStyle };\n"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,cAAc;AAEtD,SAASC,YAAYA,CAACC,OAAO,EAAEC,SAAS,EAAEC,SAAS,EAAE;EAAEC,KAAK,GAAG,CAAC;EAAEC,QAAQ;EAAEC,MAAM,GAAG,CAAC;EAAEC,UAAU,GAAG,MAAM;EAAEC,IAAI;EAAEC;AAAO,CAAC,GAAG,CAAC,CAAC,EAAE;EAC9H,MAAMC,eAAe,GAAG;IAAE,CAACR,SAAS,GAAGC;EAAU,CAAC;EAClD,IAAIM,KAAK,EACLC,eAAe,CAACC,MAAM,GAAGF,KAAK;EAClC,MAAMG,MAAM,GAAGb,uBAAuB,CAACS,IAAI,CAAC;EAC5C;AACJ;AACA;EACI,IAAIK,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EACrBF,eAAe,CAACE,MAAM,GAAGA,MAAM;EACnC,OAAOX,OAAO,CAACc,OAAO,CAACL,eAAe,EAAE;IACpCN,KAAK;IACLC,QAAQ;IACRO,MAAM,EAAE,CAACC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,GAAGA,MAAM,GAAG,QAAQ;IAClDI,IAAI,EAAE,MAAM;IACZC,UAAU,EAAEX,MAAM,GAAG,CAAC;IACtBY,SAAS,EAAEX,UAAU,KAAK,SAAS,GAAG,WAAW,GAAG;EACxD,CAAC,CAAC;AACN;AAEA,SAASP,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}