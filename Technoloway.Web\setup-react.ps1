Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   Technoloway React Landing Page Setup" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Checking Node.js installation..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    Write-Host "Node.js version: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Node.js is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Node.js 18+ from https://nodejs.org/" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "Checking npm installation..." -ForegroundColor Yellow
try {
    $npmVersion = npm --version
    Write-Host "npm version: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "ERROR: npm is not installed or not in PATH" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "Navigating to ClientApp directory..." -ForegroundColor Yellow
Set-Location -Path "ClientApp"

Write-Host ""
Write-Host "Installing React dependencies..." -ForegroundColor Yellow
Write-Host "This may take a few minutes..." -ForegroundColor Gray

try {
    npm install
    Write-Host "Dependencies installed successfully!" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Failed to install dependencies" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   Setup Complete!" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "To start the React development server:" -ForegroundColor Yellow
Write-Host "  1. Open a new terminal/command prompt" -ForegroundColor White
Write-Host "  2. Navigate to: Technoloway.Web\ClientApp" -ForegroundColor White
Write-Host "  3. Run: npm start" -ForegroundColor White
Write-Host ""
Write-Host "To access the React landing page:" -ForegroundColor Yellow
Write-Host "  1. Start your ASP.NET Core application" -ForegroundColor White
Write-Host "  2. Navigate to: https://localhost:7001/Home/React" -ForegroundColor White
Write-Host ""
Write-Host "For production build:" -ForegroundColor Yellow
Write-Host "  Run: npm run build" -ForegroundColor White
Write-Host ""

Read-Host "Press Enter to exit"
