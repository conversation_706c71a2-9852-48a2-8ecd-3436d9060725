# Technoloway Modern Landing Page

A modern, responsive React-based landing page for Technoloway software development company.

## Features

- **Modern Design**: Clean, professional design with smooth animations
- **Responsive Layout**: Optimized for all devices (mobile, tablet, desktop)
- **Dynamic Content**: Integrates with existing ASP.NET Core backend
- **Performance Optimized**: Fast loading with lazy loading and optimizations
- **SEO Friendly**: Proper meta tags and semantic HTML structure

## Components

### 🎯 Hero Section
- Dynamic slideshow support
- Animated floating tech icons
- Call-to-action buttons
- Smooth scroll indicators

### 🛠️ Services Overview
- Service cards with hover effects
- Icons and pricing display
- Project count integration
- Links to detailed service pages

### 💼 Portfolio Section
- Featured projects showcase
- Technology tags
- Project completion dates
- Client information display

### 💬 Testimonials Carousel
- Client feedback with ratings
- Auto-advancing carousel
- Star ratings display
- Client photos and company info

### 📞 Contact Form
- Form validation with React Hook Form
- Real-time error handling
- Success/error status messages
- Integration with backend ContactForm entity

### 🔗 Footer
- Company information
- Quick navigation links
- Social media links
- Contact information
- Scroll-to-top functionality

## Technology Stack

- **React 18** with TypeScript
- **Tailwind CSS** for styling
- **Framer Motion** for animations
- **React Hook Form** for form handling
- **Axios** for API calls
- **Lucide React** for modern icons

## API Integration

The React app consumes the following API endpoints:

- `GET /api/chatbot/services` - Fetch services data
- `GET /api/chatbot/projects?featuredOnly=true` - Fetch featured projects
- `GET /api/chatbot/technologies` - Fetch technologies
- `GET /api/chatbot/testimonials` - Fetch testimonials
- `POST /api/chatbot/contact` - Submit contact form

## Setup Instructions

### Prerequisites
- Node.js 18+ and npm
- .NET 9.0 SDK
- The existing Technoloway ASP.NET Core backend

### Installation

1. **Navigate to the ClientApp directory:**
   ```bash
   cd Technoloway.Web/ClientApp
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Start the development server:**
   ```bash
   npm start
   ```

4. **Build for production:**
   ```bash
   npm run build
   ```

### Integration with ASP.NET Core

1. **Access the React landing page:**
   - Navigate to `/Home/React` in your ASP.NET Core application
   - Or set it as the default route by modifying routing configuration

2. **API Configuration:**
   - The React app is configured to use `/api/chatbot` endpoints
   - Proxy is set to your ASP.NET Core backend in package.json

## Customization

### Branding
- Colors are defined in `tailwind.config.js`
- Primary brand color: `#4f46e5` (indigo)
- Secondary colors follow your existing design system

### Content
- Hero section content can be modified in `HeroSection.tsx`
- Contact information in `Footer.tsx` and `ContactSection.tsx`
- Company information throughout components

### Styling
- Tailwind CSS classes for consistent styling
- Custom animations defined in `tailwind.config.js`
- Responsive breakpoints: sm (640px), md (768px), lg (1024px), xl (1280px)

## Performance Features

- **Lazy Loading**: Images and components load as needed
- **Code Splitting**: Automatic code splitting with React
- **Optimized Animations**: Hardware-accelerated CSS animations
- **Intersection Observer**: Efficient scroll-based animations
- **Image Optimization**: Proper image sizing and formats

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Development

### File Structure
```
src/
├── components/
│   ├── layout/
│   │   └── Footer.tsx
│   ├── sections/
│   │   ├── HeroSection.tsx
│   │   ├── ServicesSection.tsx
│   │   ├── PortfolioSection.tsx
│   │   ├── TestimonialsSection.tsx
│   │   └── ContactSection.tsx
│   └── ui/
│       ├── Button.tsx
│       ├── AnimatedSection.tsx
│       └── LoadingSpinner.tsx
├── services/
│   └── api.ts
├── types/
│   └── index.ts
├── App.tsx
├── index.tsx
└── index.css
```

### Adding New Sections
1. Create component in `src/components/sections/`
2. Import and add to `App.tsx`
3. Add corresponding API endpoints if needed
4. Update types in `src/types/index.ts`

## Deployment

### Production Build
```bash
npm run build
```

### Integration Notes
- Built files will be in `build/` directory
- Configure ASP.NET Core to serve static files from build directory
- Set up proper routing for SPA fallback

## Support

For questions or issues related to the React landing page:
1. Check the browser console for errors
2. Verify API endpoints are accessible
3. Ensure all dependencies are installed
4. Check network requests in browser dev tools

## Future Enhancements

- [ ] Add blog section integration
- [ ] Implement dark mode toggle
- [ ] Add more animation variants
- [ ] Integrate with analytics
- [ ] Add A/B testing capabilities
- [ ] Implement progressive web app features
