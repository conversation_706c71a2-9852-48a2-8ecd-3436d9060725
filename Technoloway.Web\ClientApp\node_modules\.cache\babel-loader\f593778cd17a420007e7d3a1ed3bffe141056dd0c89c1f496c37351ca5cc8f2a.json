{"ast": null, "code": "import { getOrigin, checkTargetForNewValues } from '../utils/setters.mjs';\nimport { parseDomVariant } from './utils/parse-dom-variant.mjs';\nimport { VisualElement } from '../VisualElement.mjs';\nclass DOMVisualElement extends VisualElement {\n  sortInstanceNodePosition(a, b) {\n    /**\n     * compareDocumentPosition returns a bitmask, by using the bitwise &\n     * we're returning true if 2 in that bitmask is set to true. 2 is set\n     * to true if b preceeds a.\n     */\n    return a.compareDocumentPosition(b) & 2 ? 1 : -1;\n  }\n  getBaseTargetFromProps(props, key) {\n    return props.style ? props.style[key] : undefined;\n  }\n  removeValueFromRenderState(key, {\n    vars,\n    style\n  }) {\n    delete vars[key];\n    delete style[key];\n  }\n  makeTargetAnimatableFromInstance({\n    transition,\n    transitionEnd,\n    ...target\n  }, {\n    transformValues\n  }, isMounted) {\n    let origin = getOrigin(target, transition || {}, this);\n    /**\n     * If Framer has provided a function to convert `Color` etc value types, convert them\n     */\n    if (transformValues) {\n      if (transitionEnd) transitionEnd = transformValues(transitionEnd);\n      if (target) target = transformValues(target);\n      if (origin) origin = transformValues(origin);\n    }\n    if (isMounted) {\n      checkTargetForNewValues(this, target, origin);\n      const parsed = parseDomVariant(this, target, origin, transitionEnd);\n      transitionEnd = parsed.transitionEnd;\n      target = parsed.target;\n    }\n    return {\n      transition,\n      transitionEnd,\n      ...target\n    };\n  }\n}\nexport { DOMVisualElement };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "checkTargetForNewValues", "parseDomVariant", "VisualElement", "DOMVisualElement", "sortInstanceNodePosition", "a", "b", "compareDocumentPosition", "getBaseTargetFromProps", "props", "key", "style", "undefined", "removeValueFromRenderState", "vars", "makeTargetAnimatableFromInstance", "transition", "transitionEnd", "target", "transformValues", "isMounted", "origin", "parsed"], "sources": ["C:/Users/<USER>/Documents/Project By AI/Technoloway/Technoloway (Processing)/Technoloway.Web/ClientApp/node_modules/framer-motion/dist/es/render/dom/DOMVisualElement.mjs"], "sourcesContent": ["import { getOrigin, checkTargetForNewValues } from '../utils/setters.mjs';\nimport { parseDomVariant } from './utils/parse-dom-variant.mjs';\nimport { VisualElement } from '../VisualElement.mjs';\n\nclass DOMVisualElement extends VisualElement {\n    sortInstanceNodePosition(a, b) {\n        /**\n         * compareDocumentPosition returns a bitmask, by using the bitwise &\n         * we're returning true if 2 in that bitmask is set to true. 2 is set\n         * to true if b preceeds a.\n         */\n        return a.compareDocumentPosition(b) & 2 ? 1 : -1;\n    }\n    getBaseTargetFromProps(props, key) {\n        return props.style ? props.style[key] : undefined;\n    }\n    removeValueFromRenderState(key, { vars, style }) {\n        delete vars[key];\n        delete style[key];\n    }\n    makeTargetAnimatableFromInstance({ transition, transitionEnd, ...target }, { transformValues }, isMounted) {\n        let origin = getOrigin(target, transition || {}, this);\n        /**\n         * If Framer has provided a function to convert `Color` etc value types, convert them\n         */\n        if (transformValues) {\n            if (transitionEnd)\n                transitionEnd = transformValues(transitionEnd);\n            if (target)\n                target = transformValues(target);\n            if (origin)\n                origin = transformValues(origin);\n        }\n        if (isMounted) {\n            checkTargetForNewValues(this, target, origin);\n            const parsed = parseDomVariant(this, target, origin, transitionEnd);\n            transitionEnd = parsed.transitionEnd;\n            target = parsed.target;\n        }\n        return {\n            transition,\n            transitionEnd,\n            ...target,\n        };\n    }\n}\n\nexport { DOMVisualElement };\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,uBAAuB,QAAQ,sBAAsB;AACzE,SAASC,eAAe,QAAQ,+BAA+B;AAC/D,SAASC,aAAa,QAAQ,sBAAsB;AAEpD,MAAMC,gBAAgB,SAASD,aAAa,CAAC;EACzCE,wBAAwBA,CAACC,CAAC,EAAEC,CAAC,EAAE;IAC3B;AACR;AACA;AACA;AACA;IACQ,OAAOD,CAAC,CAACE,uBAAuB,CAACD,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACpD;EACAE,sBAAsBA,CAACC,KAAK,EAAEC,GAAG,EAAE;IAC/B,OAAOD,KAAK,CAACE,KAAK,GAAGF,KAAK,CAACE,KAAK,CAACD,GAAG,CAAC,GAAGE,SAAS;EACrD;EACAC,0BAA0BA,CAACH,GAAG,EAAE;IAAEI,IAAI;IAAEH;EAAM,CAAC,EAAE;IAC7C,OAAOG,IAAI,CAACJ,GAAG,CAAC;IAChB,OAAOC,KAAK,CAACD,GAAG,CAAC;EACrB;EACAK,gCAAgCA,CAAC;IAAEC,UAAU;IAAEC,aAAa;IAAE,GAAGC;EAAO,CAAC,EAAE;IAAEC;EAAgB,CAAC,EAAEC,SAAS,EAAE;IACvG,IAAIC,MAAM,GAAGtB,SAAS,CAACmB,MAAM,EAAEF,UAAU,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC;IACtD;AACR;AACA;IACQ,IAAIG,eAAe,EAAE;MACjB,IAAIF,aAAa,EACbA,aAAa,GAAGE,eAAe,CAACF,aAAa,CAAC;MAClD,IAAIC,MAAM,EACNA,MAAM,GAAGC,eAAe,CAACD,MAAM,CAAC;MACpC,IAAIG,MAAM,EACNA,MAAM,GAAGF,eAAe,CAACE,MAAM,CAAC;IACxC;IACA,IAAID,SAAS,EAAE;MACXpB,uBAAuB,CAAC,IAAI,EAAEkB,MAAM,EAAEG,MAAM,CAAC;MAC7C,MAAMC,MAAM,GAAGrB,eAAe,CAAC,IAAI,EAAEiB,MAAM,EAAEG,MAAM,EAAEJ,aAAa,CAAC;MACnEA,aAAa,GAAGK,MAAM,CAACL,aAAa;MACpCC,MAAM,GAAGI,MAAM,CAACJ,MAAM;IAC1B;IACA,OAAO;MACHF,UAAU;MACVC,aAAa;MACb,GAAGC;IACP,CAAC;EACL;AACJ;AAEA,SAASf,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}