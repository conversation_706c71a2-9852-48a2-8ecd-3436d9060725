{"ast": null, "code": "/**\n * Reset an axis to the provided origin box.\n *\n * This is a mutative operation.\n */\nfunction copyAxisInto(axis, originAxis) {\n  axis.min = originAxis.min;\n  axis.max = originAxis.max;\n}\n/**\n * Reset a box to the provided origin box.\n *\n * This is a mutative operation.\n */\nfunction copyBoxInto(box, originBox) {\n  copyAxisInto(box.x, originBox.x);\n  copyAxisInto(box.y, originBox.y);\n}\nexport { copyAxisInto, copyBoxInto };", "map": {"version": 3, "names": ["copyAxisInto", "axis", "originAxis", "min", "max", "copyBoxInto", "box", "originBox", "x", "y"], "sources": ["C:/Users/<USER>/Documents/Project By AI/Technoloway/Technoloway (Processing)/Technoloway.Web/ClientApp/node_modules/framer-motion/dist/es/projection/geometry/copy.mjs"], "sourcesContent": ["/**\n * Reset an axis to the provided origin box.\n *\n * This is a mutative operation.\n */\nfunction copyAxisInto(axis, originAxis) {\n    axis.min = originAxis.min;\n    axis.max = originAxis.max;\n}\n/**\n * Reset a box to the provided origin box.\n *\n * This is a mutative operation.\n */\nfunction copyBoxInto(box, originBox) {\n    copyAxisInto(box.x, originBox.x);\n    copyAxisInto(box.y, originBox.y);\n}\n\nexport { copyAxisInto, copyBoxInto };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,SAASA,YAAYA,CAACC,IAAI,EAAEC,UAAU,EAAE;EACpCD,IAAI,CAACE,GAAG,GAAGD,UAAU,CAACC,GAAG;EACzBF,IAAI,CAACG,GAAG,GAAGF,UAAU,CAACE,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,GAAG,EAAEC,SAAS,EAAE;EACjCP,YAAY,CAACM,GAAG,CAACE,CAAC,EAAED,SAAS,CAACC,CAAC,CAAC;EAChCR,YAAY,CAACM,GAAG,CAACG,CAAC,EAAEF,SAAS,CAACE,CAAC,CAAC;AACpC;AAEA,SAAST,YAAY,EAAEK,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}