{"ast": null, "code": "/**\n * Generate a list of every possible transform key.\n */\nconst transformPropOrder = [\"transformPerspective\", \"x\", \"y\", \"z\", \"translateX\", \"translateY\", \"translateZ\", \"scale\", \"scaleX\", \"scaleY\", \"rotate\", \"rotateX\", \"rotateY\", \"rotateZ\", \"skew\", \"skewX\", \"skewY\"];\n/**\n * A quick lookup for transform props.\n */\nconst transformProps = new Set(transformPropOrder);\nexport { transformPropOrder, transformProps };", "map": {"version": 3, "names": ["transformPropOrder", "transformProps", "Set"], "sources": ["C:/Users/<USER>/Documents/Project By AI/Technoloway/Technoloway (Processing)/Technoloway.Web/ClientApp/node_modules/framer-motion/dist/es/render/html/utils/transform.mjs"], "sourcesContent": ["/**\n * Generate a list of every possible transform key.\n */\nconst transformPropOrder = [\n    \"transformPerspective\",\n    \"x\",\n    \"y\",\n    \"z\",\n    \"translateX\",\n    \"translateY\",\n    \"translateZ\",\n    \"scale\",\n    \"scaleX\",\n    \"scaleY\",\n    \"rotate\",\n    \"rotateX\",\n    \"rotateY\",\n    \"rotateZ\",\n    \"skew\",\n    \"skewX\",\n    \"skewY\",\n];\n/**\n * A quick lookup for transform props.\n */\nconst transformProps = new Set(transformPropOrder);\n\nexport { transformPropOrder, transformProps };\n"], "mappings": "AAAA;AACA;AACA;AACA,MAAMA,kBAAkB,GAAG,CACvB,sBAAsB,EACtB,GAAG,EACH,GAAG,EACH,GAAG,EACH,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS,EACT,SAAS,EACT,MAAM,EACN,OAAO,EACP,OAAO,CACV;AACD;AACA;AACA;AACA,MAAMC,cAAc,GAAG,IAAIC,GAAG,CAACF,kBAAkB,CAAC;AAElD,SAASA,kBAAkB,EAAEC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}