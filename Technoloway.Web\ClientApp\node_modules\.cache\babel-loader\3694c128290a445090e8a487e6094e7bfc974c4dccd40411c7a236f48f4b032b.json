{"ast": null, "code": "import { isBrowser } from '../is-browser.mjs';\nimport { hasReducedMotionListener, prefersReducedMotion } from './state.mjs';\nfunction initPrefersReducedMotion() {\n  hasReducedMotionListener.current = true;\n  if (!isBrowser) return;\n  if (window.matchMedia) {\n    const motionMediaQuery = window.matchMedia(\"(prefers-reduced-motion)\");\n    const setReducedMotionPreferences = () => prefersReducedMotion.current = motionMediaQuery.matches;\n    motionMediaQuery.addListener(setReducedMotionPreferences);\n    setReducedMotionPreferences();\n  } else {\n    prefersReducedMotion.current = false;\n  }\n}\nexport { initPrefersReducedMotion };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "hasReducedMotionListener", "prefersReducedMotion", "initPrefersReducedMotion", "current", "window", "matchMedia", "motionMediaQuery", "setReducedMotionPreferences", "matches", "addListener"], "sources": ["C:/Users/<USER>/Documents/Project By AI/Technoloway/Technoloway (Processing)/Technoloway.Web/ClientApp/node_modules/framer-motion/dist/es/utils/reduced-motion/index.mjs"], "sourcesContent": ["import { isBrowser } from '../is-browser.mjs';\nimport { hasReducedMotionListener, prefersReducedMotion } from './state.mjs';\n\nfunction initPrefersReducedMotion() {\n    hasReducedMotionListener.current = true;\n    if (!isBrowser)\n        return;\n    if (window.matchMedia) {\n        const motionMediaQuery = window.matchMedia(\"(prefers-reduced-motion)\");\n        const setReducedMotionPreferences = () => (prefersReducedMotion.current = motionMediaQuery.matches);\n        motionMediaQuery.addListener(setReducedMotionPreferences);\n        setReducedMotionPreferences();\n    }\n    else {\n        prefersReducedMotion.current = false;\n    }\n}\n\nexport { initPrefersReducedMotion };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,mBAAmB;AAC7C,SAASC,wBAAwB,EAAEC,oBAAoB,QAAQ,aAAa;AAE5E,SAASC,wBAAwBA,CAAA,EAAG;EAChCF,wBAAwB,CAACG,OAAO,GAAG,IAAI;EACvC,IAAI,CAACJ,SAAS,EACV;EACJ,IAAIK,MAAM,CAACC,UAAU,EAAE;IACnB,MAAMC,gBAAgB,GAAGF,MAAM,CAACC,UAAU,CAAC,0BAA0B,CAAC;IACtE,MAAME,2BAA2B,GAAGA,CAAA,KAAON,oBAAoB,CAACE,OAAO,GAAGG,gBAAgB,CAACE,OAAQ;IACnGF,gBAAgB,CAACG,WAAW,CAACF,2BAA2B,CAAC;IACzDA,2BAA2B,CAAC,CAAC;EACjC,CAAC,MACI;IACDN,oBAAoB,CAACE,OAAO,GAAG,KAAK;EACxC;AACJ;AAEA,SAASD,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}